<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <!-- ASP.NET Core -->
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0"/>

        <!-- Entity Framework Core -->
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0"/>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0"/>

        <!-- Authentication -->
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.0"/>

        <!-- SignalR -->
        <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.1.0"/>

        <!-- Background Jobs -->
        <PackageReference Include="Hangfire.Core" Version="1.8.6"/>
        <PackageReference Include="Hangfire.PostgreSql" Version="1.20.6"/>
        <PackageReference Include="Hangfire.AspNetCore" Version="1.8.6"/>

        <!-- Docker -->
        <PackageReference Include="Docker.DotNet" Version="3.125.15"/>

        <!-- Additional utilities -->
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3"/>
        <PackageReference Include="AutoMapper" Version="12.0.1"/>
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1"/>
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0"/>
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.0"/>
        <PackageReference Include="Serilog.Sinks.PostgreSQL" Version="2.3.0"/>
    </ItemGroup>

</Project>
